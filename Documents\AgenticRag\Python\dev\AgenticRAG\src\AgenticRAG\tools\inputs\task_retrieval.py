from imports import *

import asyncio
from typing import (
    Callable,
    Sequence,
    Union,
    Any,
    Dict,
    List,
    Set,
)
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_core.tools import tool
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from llama_index.core.vector_stores import MetadataFilters
from llama_index.core.query_engine import BaseQueryEngine
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage
from langgraph.types import Command
from langchain_core.tools import BaseTool
from llama_index.core.postprocessor import LLMRerank

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from etc.rag_metrics_logger import log_rag_metrics_to_csv

@tool
async def file_path_retriever(query: str) -> str:
    """Extract and return only the file paths from documents matching the query"""
    try:
        print(f"\n[DEBUG] ===== FILE PATH RETRIEVER STARTED =====")
        print(f"[DEBUG] Original query: '{query}'")

        # Get the index from Globals
        index = Globals.get_index()

        # Configure the query engine to focus on retrieving documents
        # We use "no_text" response mode since we only care about metadata
        query_engine = index.as_query_engine(
            response_mode="no_text",
            similarity_top_k=10,
            vector_store_query_mode="hybrid",
            node_postprocessors=[
                LLMRerank(top_n=5)
            ]
        )

        # Expand the query to improve retrieval
        expanded_queries = await expand_query(query)
        print(f"[DEBUG] Successfully generated {len(expanded_queries)} query variations")

        # List of all query texts to use for retrieval
        all_retrieval_query_texts = [query] + expanded_queries
        print(f"[DEBUG] Total queries to process: {len(all_retrieval_query_texts)}")

        # Extract file paths from source nodes
        all_file_paths: Set[str] = set()

        # Process each query variation
        for query_text in all_retrieval_query_texts:
            try:
                print(f"[DEBUG] Processing query: '{query_text}'")

                # Execute the query
                response = await query_engine.aquery(query_text)

                # Try to extract source nodes
                if hasattr(response, 'source_nodes'):
                    source_nodes = response.source_nodes
                    print(f"[DEBUG] Retrieved {len(source_nodes)} nodes for file path extraction")

                    # Extract file paths from each node's metadata
                    for node in source_nodes:
                        if hasattr(node, 'metadata') and node.metadata:
                            # Check for common file path metadata keys
                            if 'file_path' in node.metadata:
                                all_file_paths.add(node.metadata['file_path'])
                            elif 'source' in node.metadata:
                                all_file_paths.add(node.metadata['source'])
                            elif 'file_name' in node.metadata:
                                all_file_paths.add(node.metadata['file_name'])
                            elif 'filename' in node.metadata:
                                all_file_paths.add(node.metadata['filename'])
                            elif 'path' in node.metadata:
                                all_file_paths.add(node.metadata['path'])
            except Exception as query_error:
                print(f"[DEBUG] Error processing query '{query_text}': {query_error}")

        print(f"[DEBUG] Extracted {len(all_file_paths)} unique file paths")
        print(f"[DEBUG] ===== FILE PATH RETRIEVER COMPLETED =====")

        # Return only the file paths, one per line
        if all_file_paths:
            return "\n".join(sorted(all_file_paths))
        else:
            return "No file paths found for your query."
    except Exception as e:
        print(f"[DEBUG] Error in file_path_retriever: {e}")
        etc.helper_functions.exception_triggered(e)
        return f"Error retrieving file paths: {e}"


@tool
async def web_search_tool(query: str) -> str:
    """Search the web for information."""
    # Define search tools for agents
    tavily_tool = TavilySearchResults(max_results=5)
    docs = tavily_tool.invoke({"query": query})
    web_results = "\n".join([d["content"] for d in docs])
    return web_results

async def retrieve_data(query_engine: BaseQueryEngine, user_input: str) -> dict:
    """Perform RAG search using the provided query engine"""
    # Note: query_engine parameter is not used as we create a new enhanced engine
    try:
        print(f"\n[DEBUG] Starting retrieval for query: '{user_input}'")

        # Get the index from Globals to create a new query engine with enhanced settings
        print("[DEBUG] Creating enhanced query engine with hybrid search")

        # Create a new query engine with advanced settings
        index = Globals.get_index()
        filters = MetadataFilters(filters=[])

        # Configure the enhanced query engine with hybrid search WITHOUT LLM reranking
        # We'll collect all nodes and do the reranking later
        enhanced_engine = index.as_query_engine(
            filters=filters,
            response_mode="no_text",  # We only want the nodes, not the summarized response
            similarity_top_k=10,      # Vector search top k
            sparse_top_k=7,           # Keyword search top k
            vector_store_query_mode="hybrid"  # Using hybrid search
        )

        # Use aquery for async operation
        print("[DEBUG] Executing query with enhanced retrieval settings...")
        response = await enhanced_engine.aquery(user_input)

        # Extract source nodes for feedback
        source_nodes = []
        vector_search_results = 10  # Default from configuration
        keyword_search_results = 7  # Default from configuration
        chunk_scores = []

        # Try to extract information about nodes
        try:
            if hasattr(response, 'source_nodes'):
                source_nodes = response.source_nodes
                print(f"[DEBUG] Retrieved {len(source_nodes)} nodes with hybrid search")

                # Extract scores from source nodes if available
                for node in source_nodes:
                    if hasattr(node, 'score') and node.score is not None:
                        chunk_scores.append(node.score)

            # Update metrics with actual values
            vector_search_results = len(source_nodes) if source_nodes else vector_search_results

        except Exception as e:
            print(f"[DEBUG] Could not extract node information: {e}")

        # Return both the source nodes and metrics
        return {
            "source_nodes": source_nodes,
            "metrics": {
                "query": user_input,
                "vector_search_results": vector_search_results,
                "keyword_search_results": keyword_search_results,
                "chunk_scores": chunk_scores
            }
        }
    except Exception as e:
        etc.helper_functions.exception_triggered(e)
        return {
            "source_nodes": [],
            "metrics": None,
            "error": f"An error occurred: {e}"
        }



async def expand_query(user_input: str) -> list[str]:
    """Generate multiple perspectives of the user query"""
    print(f"\n[DEBUG] Expanding query: '{user_input}'")

    template = """You are an AI language model assistant. Your task is to generate three
    different versions of the given user question to retrieve relevant documents.
    By generating multiple perspectives on the user question, your goal is to help
    overcome limitations of similarity search. Provide these alternative questions
    separated by newlines.

    Make sure each alternative query:
    1. Rephrases the question using different synonyms
    2. Changes the perspective of the question
    3. Breaks complex questions into simpler components


    Original question: {user_input}"""

    prompt_perspectives = ChatPromptTemplate.from_template(template)

    generate_queries = (
        prompt_perspectives
        | ChatOpenAI(temperature=0)
        | StrOutputParser()
        | (lambda x: x.split("\n"))
    )

    try:
        print("[DEBUG] Generating query variations using LLM...")
        expanded_queries = await generate_queries.ainvoke({"user_input": user_input})

        # Filter out potential empty strings or numbering (e.g., "1. Query") more robustly
        cleaned_queries = [
            q.strip() for q in expanded_queries if q and q.strip() and not q.strip().isnumeric()
        ]
        # Optional: Remove potential numbering like "1. ", "2. "
        cleaned_queries = [q.split(". ", 1)[-1] if q[0].isdigit() and q[1] == '.' else q for q in cleaned_queries]

        # Print each expanded query for debugging
        print("[DEBUG] Generated the following query variations:")
        for i, query in enumerate(cleaned_queries):
            print(f"[DEBUG]   {i+1}. {query}")

        return cleaned_queries
    except Exception as e:
        print(f"[DEBUG] Error during query expansion: {e}")
        etc.helper_functions.exception_triggered(e)
        return [] # Return empty list on failure

@tool
async def rag_search_tool(query: str):
    """Function to do RAG search using the provided index"""
    try:
        print(f"\n[DEBUG] ===== RAG SEARCH TOOL STARTED =====")
        print(f"[DEBUG] Original query: '{query}'")

        # Pass None as query_engine since it's not used in retrieve_data
        query_engine = None

        # Expand the query
        expanded_queries = await expand_query(query)
        print(f"[DEBUG] Successfully generated {len(expanded_queries)} query variations")

        # List of all query texts to use for retrieval
        all_retrieval_query_texts = [query] + expanded_queries
        print(f"[DEBUG] Total queries to process: {len(all_retrieval_query_texts)}")

        # Gather results for all queries
        print(f"[DEBUG] Starting retrieval for all query variations...")
        tasks = []
        for query_text in all_retrieval_query_texts:
            tasks.append(retrieve_data(query_engine, query_text))

        print(f"[DEBUG] Awaiting all retrieval tasks to complete...")
        all_results = await asyncio.gather(*tasks, return_exceptions=True)
        print(f"[DEBUG] All retrieval tasks completed")

        # Variables to store aggregated metrics and all source nodes
        all_metrics = []
        all_source_nodes = []
        best_metrics = None
        successful_queries = 0

        # Process results and collect all source nodes
        print(f"[DEBUG] Processing retrieval results and collecting all source nodes...")
        for i, result in enumerate(all_results):
            query_used = all_retrieval_query_texts[i]
            if isinstance(result, Exception):
                print(f"[DEBUG] Error retrieving data for query '{query_used}': {result}")
            elif result:
                successful_queries += 1

                # Extract source nodes and metrics from the result
                if isinstance(result, dict):
                    if 'source_nodes' in result and result['source_nodes']:
                        # Add source nodes to our collection
                        all_source_nodes.extend(result['source_nodes'])
                        print(f"[DEBUG] Added {len(result['source_nodes'])} nodes from query '{query_used}'")

                    # Collect metrics if available
                    if 'metrics' in result and result['metrics'] is not None:
                        all_metrics.append(result['metrics'])

                        # Use the metrics from the original query as the best metrics
                        if query_used == query and best_metrics is None:
                            best_metrics = result['metrics']

        print(f"[DEBUG] Successfully retrieved data for {successful_queries}/{len(all_retrieval_query_texts)} queries")
        print(f"[DEBUG] Collected a total of {len(all_source_nodes)} source nodes from all queries")

        # If we have no source nodes, return empty result
        if not all_source_nodes:
            print("[DEBUG] No relevant data found for your query.")
            return "No relevant information found for your query."

        # Perform LLM reranking on all collected nodes
        print(f"[DEBUG] Performing LLM reranking on all {len(all_source_nodes)} collected nodes...")

        # Create an LLM reranker
        llm_reranker = LLMRerank(top_n=7)  # Get top 7 nodes

        # Rerank all nodes
        reranked_nodes = llm_reranker.postprocess_nodes(all_source_nodes, query_str=query)
        print(f"[DEBUG] After LLM reranking, selected top {len(reranked_nodes)} nodes")

        # Extract text from reranked nodes and combine
        node_texts = []
        for node in reranked_nodes:
            if hasattr(node, 'text') and node.text:
                node_texts.append(node.text)
            elif hasattr(node, 'get_text') and callable(node.get_text):
                node_texts.append(node.get_text())
            elif hasattr(node, 'node') and hasattr(node.node, 'text'):
                node_texts.append(node.node.text)

        # Create a tree summarizer to combine the node texts
        from llama_index.core.response_synthesizers import TreeSummarize
        summarizer = TreeSummarize(verbose=True)

        # Summarize the reranked nodes
        print(f"[DEBUG] Summarizing the top {len(reranked_nodes)} nodes...")
        summary_response = await summarizer.aget_response(query, node_texts)
        print(f"[DEBUG] Successfully generated summary from top nodes")

        # Log metrics if available
        if best_metrics:
            try:
                # Extract chunk contexts for logging
                chunk_contexts = []
                for node in reranked_nodes:
                    # Extract text content
                    text = ""
                    if hasattr(node, 'text') and node.text:
                        text = node.text
                    elif hasattr(node, 'get_text') and callable(node.get_text):
                        text = node.get_text()
                    elif hasattr(node, 'node') and hasattr(node.node, 'text'):
                        text = node.node.text

                    # Extract source information
                    source = "Unknown"
                    if hasattr(node, 'metadata') and node.metadata:
                        if 'file_path' in node.metadata:
                            source = node.metadata['file_path']
                        elif 'source' in node.metadata:
                            source = node.metadata['source']
                        elif 'file_name' in node.metadata:
                            source = node.metadata['file_name']
                        elif 'filename' in node.metadata:
                            source = node.metadata['filename']
                        elif 'path' in node.metadata:
                            source = node.metadata['path']

                    # Extract score
                    score = 0.0
                    if hasattr(node, 'score') and node.score is not None:
                        score = node.score

                    # Clean and truncate text for CSV storage
                    # Remove newlines and limit length to avoid CSV issues
                    if text:
                        text = text.replace('\n', ' ').replace('\r', ' ')
                        if len(text) > 500:  # Limit text length
                            text = text[:497] + "..."

                    # Add to chunk contexts
                    chunk_contexts.append((text, source, score))

                # Update metrics with reranking information
                log_rag_metrics_to_csv(
                    query=query,
                    vector_search_results=len(all_source_nodes),
                    keyword_search_results=best_metrics.get('keyword_search_results', 0),
                    llm_reranker_results=len(reranked_nodes),
                    chunk_scores=[node.score for node in reranked_nodes if hasattr(node, 'score') and node.score is not None],
                    total_chunks_used=len(reranked_nodes),
                    chunk_contexts=chunk_contexts
                )
                print(f"[DEBUG] Successfully logged RAG metrics for query: '{query}'")
            except Exception as log_error:
                print(f"[DEBUG] Error logging RAG metrics: {log_error}")

        print(f"[DEBUG] ===== RAG SEARCH TOOL COMPLETED =====")

        # Format the final response
        final_response = f"""The following information was retrieved and synthesized from our knowledge base:

{summary_response}"""

        # Return the final response
        return etc.helper_functions.convert_key_value_to_string(
            retrieved_data=final_response
        )
    except Exception as e:
        print(f"[DEBUG] Critical error in rag_search_tool: {e}")
        return f"An error occurred: {e}"

class SupervisorTask_Retrieval(SupervisorTask_SingleAgent):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        # Currently IDENTICAL to SupervisorTask_SingleAgent
        # I've created a copy in order to allow for customisation of the prompt

        used_model = self.model
        if self._tools:
            used_model = used_model.bind_tools(self._tools)
            tools_by_name = {tool.name: tool for tool in self._tools}
        tool_prompts = [
            f"{tool.name} prompt: {tool.description}."
            for tool in self._tools
        ]

        full_prompt = self.prompt + "\n" + "\n".join(tool_prompts)
        result = await used_model.ainvoke(
            input=[
                SystemMessage(
                    content=f"You are an agent tasked with answering a question using the following tools: {tools_by_name}."
                             " Your tools are sorted in order of relevance and importance. If a tool earlier in the list could potentially solve the issue, try that one first."
                             " Given the following original_input,"
                             " determine which tools need to be called to create the best result. Each tool can only be called once."
                             " If your tool list is empty, respond to the best of your abilities with an answer to the original_input."
                             " Consider the above as your logic."
                             " Consider the below as your prompts:"
                            f"\n{full_prompt}"),
                HumanMessage(content=f"Original_input: {state.original_input}\n Current output: {state.messages[-1]}")
            ]
        )
        if self._tools:
            results = []
            call_trace = []
            for tool_call in result.tool_calls:
                tool = tools_by_name[tool_call["name"]]
                call_trace.append(self.name + ": tool " + tool.name)
                if "state" in tool_call["args"] and not tool_call["args"]["state"]:
                    tool_call["args"]["state"] = state
                observation = await tool.ainvoke(input=tool_call["args"])
                results.append(ToolMessage(content=observation, tool_call_id=tool_call["id"]))
            return Command(update={"call_trace": call_trace, "messages": results, "completed_tasks": [self.name]})

        return Command(update={"call_trace": [f"{self.name}: llm_call"], "messages": result, "completed_tasks": [self.name]})

async def create_supervisor_retrieval() -> SupervisorSupervisor:
    class TaskCreator:
        research_task: SupervisorTask_SingleAgent = None
        rag_task: SupervisorTask_SingleAgent = None
        file_path_retriever_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.research_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="web_search_task", tools=[web_search_tool], prompt=
                "You are a web search tool. Your job is to search the web for information and provide the most relevant results."))

            self.rag_task = SupervisorManager.register_task( SupervisorTask_Retrieval(name="rag_task", tools=[rag_search_tool], prompt=
                "You are the most important search engine available. You are a tool with access to the company knowledge base."))

            self.file_path_retriever_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="file_path_retriever_task", tools=[file_path_retriever], prompt=
                "You are a file path retriever. Your job is to extract and return ONLY the file paths from documents when a user asks for a document. Do not provide any additional information, just the file paths."))

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="search_supervisor", prompt=
                "You are a supervisor managing several search experts. When you are called, presume that the information already provided is incredibly insufficient and that a LOT of information needs to be retrieved." \
                "The following team works under you as supervisor:" \
                "a rag search tool number 1 priority always try to use this tool first. a web search tool. a file path retriever. When someone is asking for the file location/file path use a file path retriever"
                )) \
                 .add_task(task=self.rag_task, priority=1) \
                 .add_task(task=self.file_path_retriever_task) \
                 .add_task(task=self.research_task) \
                .compile()

                
                

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
