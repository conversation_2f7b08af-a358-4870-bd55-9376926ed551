from imports import *

import os
import json
import asyncio
import requests
import numpy as np
from xml.etree import ElementTree
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timezone
from urllib.parse import urlparse
from dotenv import load_dotenv
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
# Fix the import - using the correct Ollama client
import ollama  # Use the official Ollama Python client
from qdrant_client.http.models import PointStruct       
import uuid
from datetime import datetime
from qdrant_client.models import SparseVector

from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager

@dataclass
class ProcessedChunk:
    url: str
    chunk_number: int
    title: str
    summary: str
    content: str
    metadata: Dict[str, Any]
    embedding: List[float]

class Crawler:
    def __init__(self, embedding_model: str = LLM_MODEL, collection_name: str = "mainCollection"):
        load_dotenv()
        self.embedding_model = embedding_model
        self.collection_name = collection_name
        self.qdrant_client = QDrantManager.GetClient()
        self.browser_config = None
        self.crawler = None
        self.vector_size = EMBEDDING_SIZE

        # Get the embedding dimension from Ollama
        try:
            # Test embedding to get dimensions
            test_embedding = ollama.embeddings(
                model=self.embedding_model,
                prompt="Test embedding to get dimensions"
            )
            vector_size = len(test_embedding['embedding'])
            print(f"Detected embedding size (old): {vector_size}")
            vector_size = self.vector_size
            
            # Ensure collection exists with correct schema
            #try:
                #collection_info = self.qdrant_client.get_collection(collection_name)
                #existing_size = collection_info.config.params.vectors.size
                #if existing_size != vector_size:
                    #print(f"Warning: Collection exists with different vector size ({existing_size}). Creating new collection...")
                    #self.qdrant_client.delete_collection(collection_name)
                    #raise Exception()
            #except Exception:
                #print(f"Creating collection with vector size: {vector_size}")
                #self.qdrant_client.create_collection(
                #    collection_name=collection_name,
                #    vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
                #)
        except Exception as e:
            print(f"Error during initialization: {e}")
            raise
        
    # Move the example usage function outside the class
   # async def example_usage():
       # crawler = Crawler.setup()
        # Example 1: Crawl a single URL
        # await crawler.crawl("https://ai.pydantic.dev/", is_sitemap=False)
        
        # Example 2: Crawl a sitemap
       # await crawler.crawl("https://ai.pydantic.dev/sitemap.xml", is_sitemap=True)

    @classmethod
    async def setup_async(cls):
        """Initialize the Crawler class asynchronously"""
        print("=" * 50)
        print("Crawler Setup Started")
        print("=" * 50)
        instance = cls()
        success = await instance.test_initialization_async()  # Changed to async version
        if success:
            print("Crawler setup completed successfully")
        else:
            print("Crawler setup completed with warnings")
        print("=" * 50)
        return instance

    # @classmethod
    # def setup(cls):
    #     """Initialize the Crawler class"""
    #     print("=" * 50)
    #     print("Crawler Setup Started")
    #     print("=" * 50)
    #     instance = cls()
    #     success = instance.test_initialization()
    #     if success:
    #         print("Crawler setup completed successfully")
    #     else:
    #         print("Crawler setup completed with warnings")
    #     print("=" * 50)
    #     return instance
    
    async def test_initialization_async(self):
        """Test the crawler initialization asynchronously"""
        try:
            # Initialize the crawler
            if not self.crawler:
                self.browser_config = BrowserConfig()
                self.crawler = AsyncWebCrawler(config=self.browser_config)
                await self.crawler.start()
            
            # Test URL to verify crawler is working
            test_url = "https://example.com"
            result = await self.crawl_url(test_url)
            
            if result:
                print("✓ Crawler initialization successful")
                return True
            else:
                print("✗ Crawler initialization failed")
                return False
                
        except Exception as e:
            print(f"✗ Crawler initialization error: {e}")
            return False

    def test_initialization(self):
        """Synchronous version of test initialization"""
        return asyncio.run(self.test_initialization_async())

    async def start_crawler(self):
        """Initialize the crawler if not already initialized"""
        if not self.crawler:
            self.browser_config = BrowserConfig()
            self.crawler = AsyncWebCrawler(config=self.browser_config)
            await self.crawler.start()

    async def close_crawler(self):
        """Close the crawler properly"""
        if self.crawler:
            try:
                await self.crawler.close()  # Try close() as fallback
            except AttributeError:
                try:
                    await self.crawler.stop()
                except AttributeError:
                    print("Warning: Could not find proper close method for crawler")  
            except Exception as e:
                print(f"Error closing crawler: {e}")
            finally:
                self.crawler = None
    
    def chunk_text(self, text: str, chunk_size: int = 5000) -> List[str]:
        """Split text into chunks, respecting code blocks and paragraphs while removing duplicates."""
        # First, clean up the text by removing excessive repetition
        lines = text.split('\n')
        unique_lines = []
        seen = set()
        
        for line in lines:
            # Clean the line
            clean_line = line.strip()
            # Skip empty lines
            if not clean_line:
                continue
            # Skip image references and navigation elements
            if clean_line.startswith('![') or '→' in clean_line:
                continue
            # Hash the content to detect duplicates
            line_hash = hash(clean_line)
            if line_hash not in seen:
                seen.add(line_hash)
                unique_lines.append(line)
        
        # Rejoin the deduplicated text
        clean_text = '\n'.join(unique_lines)
        
        # Now split into chunks
        chunks = []
        start = 0
        text_length = len(clean_text)
        
        while start < text_length:
            # Calculate end position
            end = start + chunk_size
            
            # If we're at the end of the text, just take what's left
            if end >= text_length:
                chunk = clean_text[start:].strip()
                if chunk and len(chunk) > 50:  # Only add if chunk is substantial
                    chunks.append(chunk)
                break
                
            # Find a good breaking point
            # Try paragraph break first
            break_point = clean_text.rfind('\n\n', start, end)
            if break_point == -1 or break_point <= start:
                # Try sentence break
                break_point = clean_text.rfind('. ', start, end)
                if break_point == -1 or break_point <= start:
                    # If no good breaking point, use the full chunk size
                    break_point = end
            else:
                break_point += 2  # Include the paragraph break
                
            # Extract chunk
            chunk = clean_text[start:break_point].strip()
            if chunk and len(chunk) > 50:  # Only add if chunk is substantial
                chunks.append(chunk)
                
            # Move to next chunk
            start = break_point
        
        return chunks

    async def get_title_and_summary(self, chunk: str, url: str) -> Dict[str, str]:
        """Extract title and summary using Ollama."""
        system_prompt = """You are an AI that extracts titles and summaries from documentation chunks.
        You must respond with valid JSON only, in this exact format:
        {"title": "Your title here", "summary": "Your summary here"}
        For the title: Create a brief descriptive title (max 100 chars)
        For the summary: Create a concise summary (max 200 chars)"""
        
        try:
            # Use the correct Ollama API
            response = ollama.chat(
                model=os.getenv("LLM_MODEL", LLM_MODEL),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"Process this content from {url}:\n\n{chunk[:1000]}..."}
                ]
            )
            
            # Get the response content
            content = response['message']['content'].strip()
            
            # Try to parse JSON response
            try:
                result = json.loads(content)
                if isinstance(result, dict) and 'title' in result and 'summary' in result:
                    return result
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract title and summary from the text
                lines = content.split('\n')
                title = next((line for line in lines if 'title' in line.lower()), '')[:100]
                summary = next((line for line in lines if 'summary' in line.lower()), '')[:200]
                
                # Clean up the extracted text
                title = title.replace('title:', '').strip()
                summary = summary.replace('summary:', '').strip()
                
                if not title:
                    title = f"Content from {urlparse(url).path}"
                if not summary:
                    summary = chunk[:200] + "..."
                
                return {
                    "title": title,
                    "summary": summary
                }
                
        except Exception as e:
            print(f"Error getting title and summary: {e}")
            # Provide fallback title and summary
            return {
                "title": f"Content from {urlparse(url).path}",
                "summary": chunk[:200] + "..."
            }
        
    async def get_embedding(self, text: str) -> List[float]:
        """Get embedding vector from Ollama."""
        try:
            # Use the correct Ollama API for embeddings    
            embeddings = ZairaSettings.OllamaSettings().embed_model.get_text_embedding(text)
            return embeddings
        except Exception as e:
            print(f"Error getting embedding: {e}")
            return [0] * 1536  # Return zero vector on error

    async def process_chunk(self, chunk: str, chunk_number: int, url: str) -> ProcessedChunk:
        """Process a single chunk of text with duplicate detection."""
        # Skip chunks that are just repetitive content
        if chunk.count(chunk[:50]) > 2:  # If the first 50 chars repeat more than twice
            print(f"Skipping repetitive chunk {chunk_number} from {url}")
            return None
            
        # Get title and summary
        extracted = await self.get_title_and_summary(chunk, url)
        
        # Get embedding
        embedding = await self.get_embedding(chunk)
        
        # Create metadata
        metadata = {
            "source": "web_crawl",
            "chunk_size": len(chunk),
            "crawled_at": datetime.now(timezone.utc).isoformat(),
            "url_path": urlparse(url).path
        }
        
        return ProcessedChunk(
            url=url,
            chunk_number=chunk_number,
            title=extracted['title'],
            summary=extracted['summary'],
            content=chunk,
            metadata=metadata,
            embedding=embedding
        )

    async def insert_chunk(self, chunk: ProcessedChunk):
        """Insert a processed chunk into Qdrant."""
        try:
            embedded_sparse = await RetrievalManager.get_embeddings_sparse(chunk.content)
            sparse_vector = SparseVector(
                indices=embedded_sparse.indices,
                values=embedded_sparse.values
            )
             # Create point with required ID and properly structured metadata
            point = PointStruct(
                id=uuid.uuid4().int & (1<<64)-1,  # Generate integer ID from UUID
                vector={"text-dense": chunk.embedding, "text-sparse": sparse_vector},
                payload={
                    "url": chunk.url,
                    "chunk_number": chunk.chunk_number,
                    "title": chunk.title,
                    "summary": chunk.summary,
                    "content": chunk.content,
                    "crawled_at": chunk.metadata.get("crawled_at"),
                    "url_path": chunk.metadata.get("url_path"),
                    "source": chunk.metadata.get("source"),
                    "chunk_size": chunk.metadata.get("chunk_size")
                }
            )
              # Use upsert instead of insert for better reliability
            self.qdrant_client.upsert(collection_name=self.collection_name, points=[point])
            print(f"Successfully inserted chunk {chunk.chunk_number} for {chunk.url}")
        except Exception as e:
            print(f"Error inserting chunk: {e}")
            raise

    async def process_and_store_document(self, url: str, markdown: str):
        """Process a document and store its chunks in parallel."""
        # Split into chunks
        chunks = self.chunk_text(markdown)
        
        # Process chunks in parallel
        tasks = [
            self.process_chunk(chunk, i, url) 
            for i, chunk in enumerate(chunks)
        ]
        processed_chunks = await asyncio.gather(*tasks)
        
        # Store chunks in parallel
        insert_tasks = [
            self.insert_chunk(chunk) 
            for chunk in processed_chunks if chunk is not None
        ]
        await asyncio.gather(*insert_tasks)

    async def crawl_url(self, url: str, config: Optional[CrawlerRunConfig] = None):
        """Crawl a single URL and process it"""
        if not self.crawler:
            await self.start_crawler()
            
        if config is None:
            config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)
            
        try:
            result = await self.crawler.arun(
                url=url,
                config=config,
                session_id="session1"
            )
            if result.success:
                print(f"Successfully crawled: {url}")
                await self.process_and_store_document(url, result.markdown.raw_markdown)
                return True
            else:
                print(f"Failed: {url} - Error: {result.error_message}")
                return False
        except Exception as e:
            print(f"Error crawling {url}: {e}")
            return False

    async def crawl_parallel(self, urls: List[str], max_concurrent: int = 5):
        """Crawl multiple URLs in parallel with improved error handling."""
        if not self.crawler:
            await self.start_crawler()
            
        try:
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_url(url: str):
                async with semaphore:
                    try:
                        print(f"Starting crawl of: {url}")
                        success = await self.crawl_url(url)
                        if success:
                            print(f"Successfully processed: {url}")
                        else:
                            print(f"Failed to process: {url}")
                    except Exception as e:
                        print(f"Error processing {url}: {e}")
            
            # Create tasks for all URLs
            tasks = [process_url(url) for url in urls]
            
            # Process URLs in parallel
            await asyncio.gather(*tasks)
            
        finally:
            await self.close_crawler()

    async def crawl(self, url_or_sitemap: str, is_sitemap: bool = False, max_concurrent: int = 5):
        """Main crawl method - can crawl a single URL or a sitemap"""
        print(f"Starting Crawler on: {url_or_sitemap}")
        print(f"Mode: {'Sitemap' if is_sitemap else 'Single URL'}")

        if not self.crawler:
            await self.start_crawler()
            
        try:
            if is_sitemap:
                print("Fetching URLs from sitemap...")
                urls = self.get_sitemap_urls(url_or_sitemap)
                if not urls:
                    print("No URLs found in sitemap to crawl")
                    return
                    
                print(f"Found {len(urls)} URLs to crawl from sitemap")
                await self.crawl_parallel(urls, max_concurrent)
            else:
                # Single URL
                await self.crawl_url(url_or_sitemap)
        except Exception as e:
            print(f"Error during crawl operation: {e}")
        finally:
            await self.close_crawler()

    def get_sitemap_urls(self, sitemap_url: str) -> List[str]:
        """Get URLs from a sitemap."""
        try:
            print(f"Fetching sitemap from: {sitemap_url}")
            response = requests.get(sitemap_url, timeout=30)
            response.raise_for_status()
            
            # Parse the XML
            root = ElementTree.fromstring(response.content)
            
    
    
            namespaces = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}
            
            urls = [] 
             # Try each namespace
             # Try with namespace first
            locations = root.findall('.//sm:loc', namespaces)
            if not locations:
                locations = root.findall('.//loc')  

            if locations:
                urls = [loc.text for loc in locations if loc.text]
                print(f"Found {len(urls)} URLs in sitemap")
            
        # Remove duplicates while preserving order
            urls = list(dict.fromkeys(urls))
            print(f"Total unique URLs found in sitemap: {len(urls)}")
            return urls  
    
        except requests.RequestException as e:
            print(f"Network error fetching sitemap: {e}")
        except ElementTree.ParseError as e:
            print(f"XML parsing error: {e}")
        except Exception as e:
            print(f"Unexpected error: {e}")
        return []
