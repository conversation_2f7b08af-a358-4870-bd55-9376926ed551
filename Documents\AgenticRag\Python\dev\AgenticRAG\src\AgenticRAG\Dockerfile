# Use the official Python base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install git (needed for pip to clone repos)
RUN apt-get update && apt-get install -y \
	git \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxrender1 \
    libxext6 \
    poppler-utils \
    tesseract-ocr \
    pandoc \
    libmagic1 \
	docker.io \
	&& rm -rf /var/lib/apt/lists/*

# Copy dependency list and install
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application code
COPY . .

# Expose the port the app runs on
EXPOSE 41000

# Command to run the app
CMD ["python", "production.py"]
