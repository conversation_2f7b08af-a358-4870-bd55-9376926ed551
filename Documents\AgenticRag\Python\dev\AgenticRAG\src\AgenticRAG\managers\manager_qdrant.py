from imports import *

from qdrant_client import Qdrant<PERSON>lient, AsyncQdrantClient, models

class QDrantManager:
    _instance = None
    _initialized = False

    _client: QdrantClient = None
    _aclient: AsyncQdrantClient = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        instance.CreateQDrantClient()
        instance.CreateQDrantAsyncClient()
        instance._initialized = True

    @classmethod
    def GetClient(cls) -> QdrantClient:
        instance = cls.get_instance()
        if instance._client is None:
            instance.CreateQDrantClient()
        return instance._client

    @classmethod
    def GetAsyncClient(cls) -> AsyncQdrantClient:
        instance = cls.get_instance()
        if instance._aclient is None:
            instance.CreateQDrantAsyncClient()
        return instance._aclient

    def CreateQDrantClient(self) -> QdrantClient:
        host = "qdrant" if Globals.is_docker() else "localhost"
        self._client = QdrantClient(
            # you can use :memory: mode for fast and light-weight experiments,
            # it does not require to have Qdrant deployed anywhere
            # but requires qdrant-client >= 1.1.1
            # location=":memory:"
            # otherwise set Qdrant instance address with:
            # url="http://<host>:<port>"
            # otherwise set Qdrant instance with host and port:
            url=f"http://{host}:6333",
            #host="localhost",
            #port=6333
            # set API KEY for Qdrant Cloud
            # api_key="<qdrant-api-key>",
            timeout=300,
        )
        return self._client
    
    def CreateQDrantAsyncClient(self) -> AsyncQdrantClient:
        host = "qdrant" if Globals.is_docker() else "localhost"
        self._aclient = AsyncQdrantClient(
            # you can use :memory: mode for fast and light-weight experiments,
            # it does not require to have Qdrant deployed anywhere
            # but requires qdrant-client >= 1.1.1
            # location=":memory:"
            # otherwise set Qdrant instance address with:
            # url="http://<host>:<port>"
            # otherwise set Qdrant instance with host and port:
            url=f"http://{host}:6333",
            #host="localhost",
            #port=6333
            # set API KEY for Qdrant Cloud
            # api_key="<qdrant-api-key>",
            timeout=300,
        )
        return self._aclient

    @classmethod
    async def upsert(cls, id: list, text_dense: list, text_sparse, data_as_str, collection_name="mainCollection"):
        instance = cls.get_instance()
        points = []
        sparse_vector = models.SparseVector(
            indices=text_sparse.indices,
            values=text_sparse.values
        )
        point = models.PointStruct(
            id=id,  # Use _airbyte_raw_id as unique ID
            vector={"text-dense": text_dense, "text-sparse": sparse_vector},
            payload={"text":data_as_str}  # Store original JSON as metadata
        )
        points.append(point)
        await instance.GetAsyncClient().upsert(collection_name=collection_name, points=points)