from imports import *

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from endpoints.oauth_endpoint import OAuth2Verifier

async def oauth_handle_commands(self: "OAuth2Verifier", identifier, tokens):
    ret_val = True
    if identifier in self.oauth_meltano_env:
        for key, value in self.oauth_meltano_env[identifier].items():
            if key == "command":
                if value == "exit":
                    if Globals.is_docker():
                        async def delayed_exit():
                            from asyncio import sleep
                            await sleep(7)
                            exit()
                        
                        from asyncio import create_task
                        create_task(delayed_exit())
                        ret_val = False
                elif value == "crawl":
                    # Crawl the user's website in case it changed
                    site_url = await OAuth2Verifier.get_token("website")
                    if site_url != "":
                        print("Starting site-crawl")
                        from subprocess import run as subprocess_run
                        result = subprocess_run('cmd /c playwright install', shell=True, capture_output=True, text=True)
                        from inputs.crawler import Crawler
                        # Initialize the crawler
                        crawler = await Crawler.setup_async()
                        sitemap = await OAuth2Verifier.get_token("website", "refresh_token")
                        if sitemap == "":
                            # Test with a single URL
                            await crawler.crawl(site_url, is_sitemap=False)
                        else:
                            # Test with a sitemap
                            sitemap_url = f"{site_url.rstrip('/')}/{sitemap}"
                            await crawler.crawl(sitemap_url, is_sitemap=True, max_concurrent=5)
                        # Clean up
                        await crawler.close_crawler()
                        print("Finished site-crawl")
    return ret_val