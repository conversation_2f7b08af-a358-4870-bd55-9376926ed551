from imports import *
from asyncpg import Record
from uuid import NAMESPACE_DNS, uuid5
from datetime import datetime

from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_retrieval import RetrievalManager
from managers.manager_qdrant import QDrantManager

class MeltanoManager:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        instance._initialized = True

    @classmethod
    async def ConvertFilesToVectorStore(cls, folder_path):
        from os import listdir, path, remove
        from unstructured.partition.auto import partition

        print(f"Starting ConvertFilesToVectorStore for folder: {folder_path}")
        files = listdir(folder_path)
        print(f"Found {len(files)} files to process: {files}")

        total_chunks = 0
        processed_files = 0

        for filename in files:
            file_path = path.join(folder_path, filename)
            if path.isfile(file_path):
                try:
                    print(f"Processing file: {filename}")

                    # Use Unstructured to parse the file
                    print(f"  - Parsing with unstructured...")
                    unstructured = partition(filename=file_path)

                    print(f"  - Converting to list...")
                    elements = await RetrievalManager.unstructured_to_list(unstructured)

                    print(f"  - Converting to dict...")
                    original_data = await RetrievalManager.list_to_dict(elements)

                    print(f"  - Converting to markdown...")
                    markdown_content = await RetrievalManager.elements_to_markdown(data=elements, prefix="# File Data\n\n")

                    print(f"  - Chunking text (length: {len(markdown_content)} chars)...")
                    chunked_content = await RetrievalManager.chunk_text(data=markdown_content)
                    print(f"  - Created {len(chunked_content)} chunks")

                    # Process each chunk separately
                    for chunk_index, chunk in enumerate(chunked_content):
                        print(f"  - Processing chunk {chunk_index + 1}/{len(chunked_content)}")
                        embedded_dense = await RetrievalManager.get_embeddings_dense(chunk)
                        embedded_sparse = await RetrievalManager.get_embeddings_sparse(chunk)
                        # Create unique ID for each chunk
                        chunk_id = str(uuid5(NAMESPACE_DNS, f"{filename}_chunk_{chunk_index}"))
                        await QDrantManager.upsert(id=chunk_id, text_dense=embedded_dense, text_sparse=embedded_sparse, data_as_str=chunk)

                    remove(file_path)
                    total_chunks += len(chunked_content)
                    processed_files += 1
                    print(f"✓ Successfully converted {filename} to Vector Store with {len(chunked_content)} chunks. File deleted.")

                except Exception as e:
                    print(f"✗ Error processing file {filename}: {e}")
                    import traceback
                    traceback.print_exc()
                    # Don't remove the file if there was an error
                    continue

        print(f"🎉 Processing complete! {processed_files} files processed, {total_chunks} total chunks created.")

    @classmethod
    async def ConvertSQLToVectorStore(cls):
        # Connect to the database using a direct connection
        await PostgreSQLManager.connect_to_database("meltanodb")
        # Get the table names
        table_names = await PostgreSQLManager.get_table_names("meltanodb")
        # Execute a query to fetch data
        for table_name in table_names:
            raw_data = await PostgreSQLManager.execute_query("meltanodb", "SELECT * FROM public." + table_name)
            #ids, jsons = zip(*raw_data)
            for record_index, json_data in enumerate(raw_data):
                record: Record = json_data
                # json_to_elements returns key/value pair. get_embeddings expects a str, List[str] or List[List[str]], so we convert to markdown first
                elements = dict(record)
                markdown_content = await RetrievalManager.elements_to_markdown(data=elements, prefix="# JSON Data\n\n")
                chunked_content = await RetrievalManager.chunk_text(data=markdown_content)

                # Process each chunk separately
                for chunk_index, chunk in enumerate(chunked_content):
                    embedded_dense = await RetrievalManager.get_embeddings_dense(chunk)
                    embedded_sparse = await RetrievalManager.get_embeddings_sparse(chunk)
                    # Create unique ID for each chunk
                    record_id = str(next(record.values()))
                    chunk_id = str(uuid5(NAMESPACE_DNS, f"{table_name}_record_{record_index}_chunk_{chunk_index}_{record_id}"))
                    await QDrantManager.upsert(id=chunk_id, text_dense=embedded_dense, text_sparse=embedded_sparse, data_as_str=chunk)
        await PostgreSQLManager.close_connection("meltanodb")
