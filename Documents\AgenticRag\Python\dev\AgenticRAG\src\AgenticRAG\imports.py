# imports.py

# ------------------------------------------------
# -            Standard library imports          -
# ------------------------------------------------
from os import environ

# ------------------------------------------------
# -             Third-party imports              -
# ------------------------------------------------

# ------------------------------------------------
# - Local application / library specific imports -
# ------------------------------------------------
import etc.helper_functions

# ------------------------------------------------
# -     Common functions or configurations       -
# ------------------------------------------------
from globals import *
from config import *
from etc.ZairaSettings import *

# Set up API keys
environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
environ["TAVILY_API_KEY"] = '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'

# Configuring LangChain settings
environ["LANGCHAIN_TRACING_V2"] = "true"
environ["LANGCHAIN_PROJECT"] = "Zaira retrieval"
environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
environ["LANGCHAIN_API_KEY"] = "***************************************************"
